import 'package:get/get.dart';
import 'package:hive/hive.dart';
import '../models/smart_composer_models.dart';
import '../models/novel.dart';
import '../models/text_modification.dart';
import '../services/smart_composer_service.dart';
import '../services/text_diff_service.dart';
import '../controllers/api_config_controller.dart';
import 'ai_file_editor_controller.dart';

/// Smart Composer 控制器
/// 管理 AI 写作助手的状态和配置
class SmartComposerController extends GetxController {
  static const String _settingsBoxName = 'smart_composer_settings';
  static const String _sessionsBoxName = 'smart_composer_sessions';
  
  late Box _settingsBox;
  late Box _sessionsBox;
  
  final SmartComposerService _service = SmartComposerService();
  final AIFileEditorController _fileEditorController = Get.put(AIFileEditorController());

  // 响应式状态
  final Rx<SmartComposerSettings> settings = SmartComposerSettings(
    providers: SmartComposerDefaults.defaultProviders,
    chatModels: SmartComposerDefaults.defaultChatModels,
    defaultChatModelId: SmartComposerDefaults.defaultChatModels.first.id,
    systemPrompt: SmartComposerDefaults.defaultSystemPrompt,
  ).obs;

  // 创作模式相关状态
  final RxBool isCreativeMode = false.obs;
  final RxString originalChapterContent = ''.obs;
  final RxList<TextModification> pendingModifications = <TextModification>[].obs;
  
  final RxList<ChatSession> chatSessions = <ChatSession>[].obs;
  final Rx<ChatSession?> currentSession = Rx<ChatSession?>(null);
  final RxBool isLoading = false.obs;
  final RxString error = ''.obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeBoxes();
    await _loadSettings();
    await _loadChatSessions();
    await syncFromApiConfig(); // 自动同步现有API配置
  }

  /// 初始化 Hive 存储盒子
  Future<void> _initializeBoxes() async {
    _settingsBox = await Hive.openBox(_settingsBoxName);
    _sessionsBox = await Hive.openBox(_sessionsBoxName);
  }

  /// 加载设置
  Future<void> _loadSettings() async {
    try {
      final settingsData = _settingsBox.get('settings');
      if (settingsData != null) {
        settings.value = SmartComposerSettings.fromJson(
          Map<String, dynamic>.from(settingsData as Map)
        );
      }
    } catch (e) {
      print('加载 Smart Composer 设置失败: $e');
    }
  }

  /// 保存设置
  Future<void> saveSettings() async {
    try {
      await _settingsBox.put('settings', settings.value.toJson());
    } catch (e) {
      print('保存 Smart Composer 设置失败: $e');
      error.value = '保存设置失败: $e';
    }
  }

  /// 加载聊天会话
  Future<void> _loadChatSessions() async {
    try {
      final sessions = <ChatSession>[];
      for (final key in _sessionsBox.keys) {
        final sessionData = _sessionsBox.get(key);
        if (sessionData != null) {
          final session = ChatSession.fromJson(
            Map<String, dynamic>.from(sessionData as Map)
          );
          sessions.add(session);
        }
      }
      
      // 按创建时间排序
      sessions.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      chatSessions.value = sessions;
    } catch (e) {
      print('加载聊天会话失败: $e');
    }
  }

  /// 保存聊天会话
  Future<void> _saveChatSession(ChatSession session) async {
    try {
      await _sessionsBox.put(session.id, session.toJson());
      
      // 更新本地列表
      final index = chatSessions.indexWhere((s) => s.id == session.id);
      if (index >= 0) {
        chatSessions[index] = session;
      } else {
        chatSessions.insert(0, session);
      }
    } catch (e) {
      print('保存聊天会话失败: $e');
      error.value = '保存会话失败: $e';
    }
  }

  /// 创建新的聊天会话
  ChatSession createNewSession({
    String? title,
    String? novelId,
    int? chapterNumber,
    Map<String, dynamic>? context,
  }) {
    final session = ChatSession(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title ?? '新对话',
      messages: [],
      createdAt: DateTime.now(),
      novelId: novelId,
      chapterNumber: chapterNumber,
      context: context,
    );
    
    currentSession.value = session;
    return session;
  }

  /// 发送消息
  Future<void> sendMessage({
    required String content,
    Novel? novel,
    Chapter? chapter,
    List<Chapter>? referencedChapters,
  }) async {
    if (currentSession.value == null) {
      error.value = '没有活动的聊天会话';
      return;
    }

    try {
      isLoading.value = true;
      error.value = '';

      // 获取当前模型和提供商
      final modelId = settings.value.defaultChatModelId;
      if (modelId == null) {
        throw Exception('未配置默认模型');
      }

      final model = settings.value.chatModels.firstWhere(
        (m) => m.id == modelId,
        orElse: () => throw Exception('找不到指定的模型'),
      );

      final provider = settings.value.providers.firstWhere(
        (p) => p.id == model.providerId,
        orElse: () => throw Exception('找不到指定的提供商'),
      );

      // 创建用户消息
      final userMessage = _service.createUserMessage(content);
      
      // 更新会话
      final updatedMessages = List<ChatMessage>.from(currentSession.value!.messages)
        ..add(userMessage);
      
      currentSession.value = currentSession.value!.copyWith(
        messages: updatedMessages,
        updatedAt: DateTime.now(),
      );

      // 构建memory上下文
      Map<String, dynamic>? memoryContext;
      if (novel != null) {
        memoryContext = _buildMemoryContext(novel, chapter, referencedChapters);
      }

      // 发送到 AI 服务
      final response = await _service.sendChatMessage(
        model: model,
        provider: provider,
        messages: updatedMessages,
        systemPrompt: settings.value.systemPrompt,
        memoryContext: memoryContext,
      );

      // 解析AI响应，检测是否为创作模式修改建议
      final assistantMessage = _service.parseAIResponse(response);
      
      // 更新会话
      final finalMessages = List<ChatMessage>.from(updatedMessages)
        ..add(assistantMessage);
      
      currentSession.value = currentSession.value!.copyWith(
        messages: finalMessages,
        updatedAt: DateTime.now(),
      );

      // 保存会话
      await _saveChatSession(currentSession.value!);

    } catch (e) {
      error.value = '发送消息失败: $e';
      print('发送消息失败: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// 添加提供商
  void addProvider(LLMProvider provider) {
    final updatedProviders = List<LLMProvider>.from(settings.value.providers)
      ..add(provider);
    
    settings.value = settings.value.copyWith(providers: updatedProviders);
    saveSettings();
  }

  /// 更新提供商
  void updateProvider(LLMProvider provider) {
    final updatedProviders = settings.value.providers.map((p) {
      return p.id == provider.id ? provider : p;
    }).toList();
    
    settings.value = settings.value.copyWith(providers: updatedProviders);
    saveSettings();
  }

  /// 删除提供商
  void removeProvider(String providerId) {
    final updatedProviders = settings.value.providers
        .where((p) => p.id != providerId)
        .toList();
    
    settings.value = settings.value.copyWith(providers: updatedProviders);
    saveSettings();
  }

  /// 添加聊天模型
  void addChatModel(ChatModel model) {
    final updatedModels = List<ChatModel>.from(settings.value.chatModels)
      ..add(model);
    
    settings.value = settings.value.copyWith(chatModels: updatedModels);
    saveSettings();
  }

  /// 更新聊天模型
  void updateChatModel(ChatModel model) {
    final updatedModels = settings.value.chatModels.map((m) {
      return m.id == model.id ? model : m;
    }).toList();
    
    settings.value = settings.value.copyWith(chatModels: updatedModels);
    saveSettings();
  }

  /// 删除聊天模型
  void removeChatModel(String modelId) {
    final updatedModels = settings.value.chatModels
        .where((m) => m.id != modelId)
        .toList();
    
    settings.value = settings.value.copyWith(chatModels: updatedModels);
    saveSettings();
  }

  /// 设置默认模型
  void setDefaultModel(String modelId) {
    settings.value = settings.value.copyWith(defaultChatModelId: modelId);
    saveSettings();
  }

  /// 更新系统提示
  void updateSystemPrompt(String prompt) {
    settings.value = settings.value.copyWith(systemPrompt: prompt);
    saveSettings();
  }

  /// 删除聊天会话
  Future<void> deleteChatSession(String sessionId) async {
    try {
      await _sessionsBox.delete(sessionId);
      chatSessions.removeWhere((s) => s.id == sessionId);
      
      if (currentSession.value?.id == sessionId) {
        currentSession.value = null;
      }
    } catch (e) {
      error.value = '删除会话失败: $e';
    }
  }

  /// 切换到指定会话
  void switchToSession(ChatSession session) {
    currentSession.value = session;
  }

  /// 获取可用的提供商
  List<LLMProvider> get availableProviders => settings.value.providers;

  /// 获取可用的模型
  List<ChatModel> get availableModels => settings.value.chatModels;

  /// 获取当前默认模型
  ChatModel? get defaultModel {
    final modelId = settings.value.defaultChatModelId;
    if (modelId == null) return null;
    
    try {
      return settings.value.chatModels.firstWhere((m) => m.id == modelId);
    } catch (e) {
      return null;
    }
  }

  /// 检查提供商是否已配置
  bool isProviderConfigured(String providerId) {
    final provider = settings.value.providers
        .where((p) => p.id == providerId)
        .firstOrNull;
    
    if (provider == null) return false;
    
    // 检查是否需要 API Key
    switch (provider.type) {
      case LLMProviderType.openai:
      case LLMProviderType.anthropic:
      case LLMProviderType.gemini:
      case LLMProviderType.deepseek:
      case LLMProviderType.openaiCompatible:
      case LLMProviderType.openrouter:
      case LLMProviderType.groq:
      case LLMProviderType.perplexity:
      case LLMProviderType.mistral:
      case LLMProviderType.azureOpenai:
      case LLMProviderType.morph:
        return provider.apiKey != null && provider.apiKey!.isNotEmpty;
      case LLMProviderType.ollama:
      case LLMProviderType.lmStudio:
        return true; // 本地模型不需要 API Key
      default:
        return provider.apiKey != null && provider.apiKey!.isNotEmpty;
    }
  }

  /// 从现有API配置同步到Smart Composer
  Future<void> syncFromApiConfig({bool force = false}) async {
    try {
      final apiConfig = Get.find<ApiConfigController>();

      // 检查当前选中的模型是否已经同步
      final currentApiModel = apiConfig.currentModel.value;
      final currentDefaultModel = defaultModel;

      // 如果当前API模型已经是默认模型，且有配置，则不需要重复同步
      if (currentDefaultModel != null &&
          currentDefaultModel.model == currentApiModel.model &&
          isProviderConfigured(currentDefaultModel.providerId)) {
        return;
      }

      final providers = <LLMProvider>[];
      final models = <ChatModel>[];

      // 从当前模型配置同步
      if (currentApiModel.apiKey.isNotEmpty) {
        final syncResult = _createProviderAndModel(currentApiModel, 'current');
        if (syncResult != null) {
          providers.add(syncResult['provider']);
          models.add(syncResult['model']);
        }
      }

      // 从所有配置的模型中同步
      for (final modelConfig in apiConfig.models) {
        if (modelConfig.apiKey.isNotEmpty && modelConfig != currentApiModel) {
          final syncResult = _createProviderAndModel(modelConfig, modelConfig.name.toLowerCase().replaceAll(' ', '-'));
          if (syncResult != null) {
            // 检查是否已经添加了相同的提供商
            if (!providers.any((p) => p.id == syncResult['provider'].id)) {
              providers.add(syncResult['provider']);
            }
            models.add(syncResult['model']);
          }
        }
      }

      // 更新Smart Composer配置
      if (providers.isNotEmpty) {
        final currentSettings = settings.value;

        // 清除之前同步的配置，避免重复
        final filteredProviders = currentSettings.providers
            .where((p) => !p.id.contains('-current') && !p.id.contains('-synced'))
            .toList();
        final filteredModels = currentSettings.chatModels
            .where((m) => !m.providerId.contains('-current') && !m.providerId.contains('-synced'))
            .toList();

        // 找到当前API模型对应的Smart Composer模型
        String? newDefaultModelId;
        for (final model in models) {
          if (model.model == currentApiModel.model) {
            newDefaultModelId = model.id;
            break;
          }
        }

        final updatedSettings = currentSettings.copyWith(
          providers: [...filteredProviders, ...providers],
          chatModels: [...filteredModels, ...models],
          defaultChatModelId: newDefaultModelId ?? (models.isNotEmpty ? models.first.id : currentSettings.defaultChatModelId),
        );

        settings.value = updatedSettings;
        await saveSettings();

        print('Smart Composer: 已自动同步 ${providers.length} 个AI提供商和 ${models.length} 个模型');
        print('Smart Composer: 默认模型设置为: ${updatedSettings.defaultChatModelId}');
      }
    } catch (e) {
      print('Smart Composer: 同步API配置失败: $e');
    }
  }

  /// 根据模型配置创建提供商和模型
  Map<String, dynamic>? _createProviderAndModel(dynamic modelConfig, String suffix) {
    LLMProviderType providerType;
    String providerId;

    // 根据模型名称或API URL判断提供商类型
    final name = modelConfig.name.toLowerCase();
    final url = modelConfig.apiUrl.toLowerCase();

    if (url.contains('api.openai.com')) {
      // 只有真正的OpenAI官方API才使用openai类型
      providerType = LLMProviderType.openai;
      providerId = 'openai-$suffix';
    } else if (name.contains('claude') || url.contains('anthropic')) {
      providerType = LLMProviderType.anthropic;
      providerId = 'claude-$suffix';
    } else if (name.contains('gemini') || url.contains('googleapis')) {
      providerType = LLMProviderType.gemini;
      providerId = 'gemini-$suffix';
    } else if (name.contains('deepseek') || url.contains('deepseek')) {
      providerType = LLMProviderType.deepseek;
      providerId = 'deepseek-$suffix';
    } else {
      // 所有其他提供商（包括代理、中转站等）都使用OpenAI兼容
      providerType = LLMProviderType.openaiCompatible;
      providerId = 'custom-$suffix';
    }

    // 构建完整的baseUrl（包含apiPath）
    String? fullBaseUrl;
    if (modelConfig.apiUrl.isNotEmpty) {
      if (modelConfig.apiPath.isNotEmpty) {
        // 组合apiUrl和apiPath
        String url = modelConfig.apiUrl;
        String path = modelConfig.apiPath;

        // 确保URL格式正确
        if (!url.endsWith('/') && !path.startsWith('/')) {
          fullBaseUrl = '$url/$path';
        } else if (url.endsWith('/') && path.startsWith('/')) {
          fullBaseUrl = '${url.substring(0, url.length - 1)}$path';
        } else {
          fullBaseUrl = '$url$path';
        }

        // 移除末尾的/chat/completions，因为SmartComposerService会自动添加
        if (fullBaseUrl.endsWith('/chat/completions')) {
          fullBaseUrl = fullBaseUrl.substring(0, fullBaseUrl.length - '/chat/completions'.length);
        }
      } else {
        fullBaseUrl = modelConfig.apiUrl;
      }
    }

    final provider = LLMProvider(
      id: providerId,
      type: providerType,
      apiKey: modelConfig.apiKey,
      baseUrl: fullBaseUrl,
    );

    final chatModel = ChatModel(
      id: '${modelConfig.name}-${modelConfig.model}',
      model: modelConfig.model,
      providerId: providerId,
      providerType: providerType,
    );

    return {
      'provider': provider,
      'model': chatModel,
    };
  }

  /// 发送文件编辑消息
  Future<void> sendFileEditMessage({
    required String content,
    required String filePath,
    String? selectedText,
    int? startLine,
    int? endLine,
    Novel? novel,
    Chapter? chapter,
  }) async {
    if (currentSession.value == null) {
      error.value = '没有活动的会话';
      return;
    }

    isLoading.value = true;
    error.value = '';

    try {
      // 首先打开文件
      final fileOpened = await _fileEditorController.openFile(filePath);
      if (!fileOpened) {
        throw Exception('无法打开文件: $filePath');
      }

      // 获取模型配置
      final modelId = settings.value.defaultChatModelId;
      if (modelId == null) {
        throw Exception('未配置默认模型');
      }

      final model = settings.value.chatModels.firstWhere(
        (m) => m.id == modelId,
        orElse: () => throw Exception('找不到指定的模型'),
      );

      final provider = settings.value.providers.firstWhere(
        (p) => p.id == model.providerId,
        orElse: () => throw Exception('找不到指定的提供商'),
      );

      // 创建用户消息
      final userMessage = _service.createUserMessage(content);

      // 更新会话
      final updatedMessages = List<ChatMessage>.from(currentSession.value!.messages)
        ..add(userMessage);

      currentSession.value = currentSession.value!.copyWith(
        messages: updatedMessages,
        updatedAt: DateTime.now(),
      );

      // 请求AI编辑文件
      await _fileEditorController.requestAIEdit(
        instruction: content,
        model: model,
        provider: provider,
        selectedText: selectedText,
        startLine: startLine,
        endLine: endLine,
      );

      // 创建助手回复
      final assistantMessage = _service.createAssistantMessage(
        '我已经分析了您的编辑请求，请查看待处理的编辑建议。您可以选择应用或拒绝这些修改。'
      );

      // 更新会话
      final finalMessages = List<ChatMessage>.from(updatedMessages)
        ..add(assistantMessage);

      currentSession.value = currentSession.value!.copyWith(
        messages: finalMessages,
        updatedAt: DateTime.now(),
      );

      // 保存会话
      await _saveChatSession(currentSession.value!);

    } catch (e) {
      error.value = '文件编辑请求失败: $e';
      print('文件编辑请求失败: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// 获取文件编辑器控制器
  AIFileEditorController get fileEditorController => _fileEditorController;

  /// 检查消息是否为文件编辑请求
  bool isFileEditRequest(String content) {
    final editKeywords = ['编辑文件', '修改文件', '更新文件', '改写', '重构', '优化代码'];
    final lowerContent = content.toLowerCase();
    return editKeywords.any((keyword) => lowerContent.contains(keyword.toLowerCase()));
  }

  /// 构建memory上下文
  Map<String, dynamic> _buildMemoryContext(
    Novel novel,
    Chapter? currentChapter,
    List<Chapter>? referencedChapters
  ) {
    final memoryContext = <String, dynamic>{};

    // 添加小说基本信息
    memoryContext['novel_info'] = {
      'title': novel.title,
      'genre': novel.genre,
      'outline': novel.outline,
      'style': novel.writingStyle ?? '',
    };

    // 添加当前章节基本信息（内容在提示词中提供，避免重复）
    if (currentChapter != null) {
      memoryContext['current_chapter'] = {
        'number': currentChapter.number,
        'title': currentChapter.title,
        'content_note': '当前章节内容已在提示词中提供，用于精确文本匹配',
      };
    }

    // 添加引用的章节
    if (referencedChapters != null && referencedChapters.isNotEmpty) {
      memoryContext['referenced_chapters'] = referencedChapters.map((chapter) => {
        'number': chapter.number,
        'title': chapter.title,
        'content': chapter.content,
      }).toList();
    }

    // 添加上下文信息
    memoryContext['context_info'] = {
      'total_chapters': novel.chapters.length,
      'current_chapter_index': currentChapter?.number ?? 0,
      'timestamp': DateTime.now().toIso8601String(),
    };

    return memoryContext;
  }

  /// 切换创作模式
  void switchCreativeMode(bool enabled, {String? chapterContent}) {
    isCreativeMode.value = enabled;
    if (enabled && chapterContent != null) {
      originalChapterContent.value = chapterContent;
    } else {
      originalChapterContent.value = '';
      pendingModifications.clear();
    }
  }

  /// 处理修改建议状态变更
  void updateModificationStatus(String messageId, String modificationId, ModificationStatus status) {
    final session = currentSession.value;
    if (session == null) return;

    final updatedMessages = session.messages.map((message) {
      if (message.id == messageId && message.isCreativeEdit && message.editResponse != null) {
        final updatedEditResponse = message.editResponse!.copyWithModificationStatus(modificationId, status);
        return message.copyWith(editResponse: updatedEditResponse);
      }
      return message;
    }).toList();

    currentSession.value = session.copyWith(
      messages: updatedMessages,
      updatedAt: DateTime.now(),
    );

    _saveChatSession(currentSession.value!);
  }

  /// 批量更新修改建议状态
  void updateBatchModificationStatus(String messageId, ModificationStatus status) {
    final session = currentSession.value;
    if (session == null) return;

    final updatedMessages = session.messages.map((message) {
      if (message.id == messageId && message.isCreativeEdit && message.editResponse != null) {
        final updatedEditResponse = message.editResponse!.copyWithBatchStatus(status);
        return message.copyWith(editResponse: updatedEditResponse);
      }
      return message;
    }).toList();

    currentSession.value = session.copyWith(
      messages: updatedMessages,
      updatedAt: DateTime.now(),
    );

    _saveChatSession(currentSession.value!);
  }

  /// 应用已接受的修改到章节内容
  String applyAcceptedModifications(String messageId) {
    final session = currentSession.value;
    if (session == null) return originalChapterContent.value;

    final message = session.messages.firstWhere(
      (msg) => msg.id == messageId && msg.isCreativeEdit,
      orElse: () => throw Exception('未找到指定的修改建议消息'),
    );

    if (message.editResponse == null) {
      return originalChapterContent.value;
    }

    return TextDiffService.applyModifications(
      originalChapterContent.value,
      message.editResponse!.acceptedModifications,
    );
  }

  /// 预览修改效果
  String previewModifications(String messageId) {
    final session = currentSession.value;
    if (session == null) return originalChapterContent.value;

    final message = session.messages.firstWhere(
      (msg) => msg.id == messageId && msg.isCreativeEdit,
      orElse: () => throw Exception('未找到指定的修改建议消息'),
    );

    if (message.editResponse == null) {
      return originalChapterContent.value;
    }

    return TextDiffService.previewModifications(
      originalChapterContent.value,
      message.editResponse!.acceptedModifications,
    );
  }

  @override
  void onClose() {
    _settingsBox.close();
    _sessionsBox.close();
    super.onClose();
  }
}
