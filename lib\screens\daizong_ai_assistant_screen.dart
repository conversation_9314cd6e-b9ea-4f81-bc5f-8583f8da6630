import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/smart_composer_controller.dart';
import '../controllers/novel_controller.dart';
import '../controllers/api_config_controller.dart';
import '../models/novel.dart';
import '../models/smart_composer_models.dart';
import '../models/text_modification.dart';
import '../widgets/enhanced_chat_message_widget.dart';
import '../langchain/prompts/novel_prompt_templates_enhanced.dart';

/// 岱宗AI辅助助手界面
/// 类似于Cursor的体验，左侧编辑器，右侧AI聊天框
/// 增强版本，支持章节内容的智能修改建议
class DaizongAIAssistantScreen extends StatefulWidget {
  final Novel novel;
  final int? initialChapterIndex;

  const DaizongAIAssistantScreen({
    super.key,
    required this.novel,
    this.initialChapterIndex,
  });

  @override
  State<DaizongAIAssistantScreen> createState() => _DaizongAIAssistantScreenState();
}

class _DaizongAIAssistantScreenState extends State<DaizongAIAssistantScreen> {
  final SmartComposerController _smartComposerController = Get.find<SmartComposerController>();
  final NovelController _novelController = Get.find<NovelController>();
  final ApiConfigController _apiConfig = Get.find<ApiConfigController>();
  
  final TextEditingController _titleController = TextEditingController();
  final List<TextEditingController> _chapterControllers = [];
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _chatScrollController = ScrollController();
  final ScrollController _editorScrollController = ScrollController();

  late Novel _currentNovel;
  int _currentChapterIndex = 0;
  bool _hasChanges = false;
  bool _showAIPanel = true;
  double _aiPanelWidth = 400;
  ChatSession? _currentSession;

  // 新增：修改建议相关状态
  final RxBool _isGeneratingSuggestions = false.obs;

  // 新增：双模式功能状态
  final RxBool _isCreativeMode = false.obs; // false: 聊天模式, true: 创作模式
  final RxList<TextModification> _pendingModifications = <TextModification>[].obs;
  final RxString _originalChapterContent = ''.obs;

  // 缓存已解析的修改，避免重复解析
  final Map<String, List<TextModification>> _parsedModificationsCache = {};

  // 章节引用功能
  final RxList<Chapter> _referencedChapters = <Chapter>[].obs;

  @override
  void initState() {
    super.initState();
    _currentNovel = widget.novel;
    _currentChapterIndex = widget.initialChapterIndex ?? 0;
    
    _titleController.text = _currentNovel.title;
    _initChapterControllers();
    _initAISession();
  }

  void _initChapterControllers() {
    _chapterControllers.clear();
    for (final chapter in _currentNovel.chapters) {
      final controller = TextEditingController(text: chapter.content);
      controller.addListener(_onTextChanged);
      _chapterControllers.add(controller);
    }
  }

  void _initAISession() {
    _currentSession = _smartComposerController.createNewSession(
      title: '《${_currentNovel.title}》岱宗AI辅助助手',
      novelId: _currentNovel.id,
      chapterNumber: _currentChapterIndex + 1,
      context: {
        'novel_title': _currentNovel.title,
        'novel_genre': _currentNovel.genre,
        'novel_outline': _currentNovel.outline,
        'chapter_count': _currentNovel.chapters.length,
        'current_chapter': _currentChapterIndex + 1,
      },
    );
  }

  void _onTextChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: TextField(
          controller: _titleController,
          style: const TextStyle(color: Colors.white),
          decoration: const InputDecoration(
            border: InputBorder.none,
            hintText: '输入小说标题',
            hintStyle: TextStyle(color: Colors.white70),
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(_showAIPanel ? Icons.close_fullscreen : Icons.open_in_full),
            tooltip: _showAIPanel ? '隐藏岱宗AI助手' : '显示岱宗AI助手',
            onPressed: () {
              setState(() {
                _showAIPanel = !_showAIPanel;
              });
            },
          ),

          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _hasChanges ? _saveChanges : null,
            tooltip: '保存',
          ),
        ],
      ),
      body: Row(
        children: [
          // 左侧编辑器
          Expanded(
            flex: _showAIPanel ? 3 : 1,
            child: _buildEditor(),
          ),
          
          // 右侧AI助手面板
          if (_showAIPanel) ...[
            Container(
              width: 1,
              color: Theme.of(context).dividerColor,
            ),
            SizedBox(
              width: _aiPanelWidth,
              child: _buildAIPanel(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEditor() {
    return Column(
      children: [
        // 章节选择器
        if (_currentNovel.chapters.isNotEmpty) _buildChapterSelector(),
        
        // 编辑器内容
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: _buildEditorContent(),
          ),
        ),
      ],
    );
  }

  Widget _buildChapterSelector() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          const Text('章节：'),
          const SizedBox(width: 8),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: _currentNovel.chapters.asMap().entries.map((entry) {
                  final index = entry.key;
                  final chapter = entry.value;
                  final isSelected = index == _currentChapterIndex;
                  
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: ChoiceChip(
                      label: Text('第${chapter.number}章'),
                      selected: isSelected,
                      onSelected: (selected) {
                        if (selected) {
                          _switchToChapter(index);
                        }
                      },
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditorContent() {
    if (_currentNovel.chapters.isEmpty) {
      return const Center(
        child: Text('暂无章节内容'),
      );
    }

    if (_currentChapterIndex >= _chapterControllers.length) {
      return const Center(
        child: Text('章节索引超出范围'),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 章节标题
        Text(
          _currentNovel.chapters[_currentChapterIndex].title,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        
        // 章节内容编辑器
        Expanded(
          child: TextField(
            controller: _chapterControllers[_currentChapterIndex],
            maxLines: null,
            expands: true,
            textAlignVertical: TextAlignVertical.top,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: '在此编辑章节内容...',
            ),
            style: const TextStyle(
              fontSize: 16,
              height: 1.6,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAIPanel() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          left: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Column(
        children: [
          // AI面板标题栏
          _buildAIPanelHeader(),

          // 聊天消息列表
          Expanded(
            child: _buildChatMessages(),
          ),

          // 输入框
          _buildChatInput(),
        ],
      ),
    );
  }

  Widget _buildAIPanelHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.auto_fix_high,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  '岱宗AI辅助助手',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // 模式切换开关
          Obx(() => Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Theme.of(context).dividerColor,
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () => _switchMode(false),
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                          decoration: BoxDecoration(
                            color: !_isCreativeMode.value
                                ? Theme.of(context).primaryColor
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.chat_bubble_outline,
                                size: 16,
                                color: !_isCreativeMode.value
                                    ? Colors.white
                                    : Theme.of(context).textTheme.bodyMedium?.color,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '聊天模式',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  color: !_isCreativeMode.value
                                      ? Colors.white
                                      : Theme.of(context).textTheme.bodyMedium?.color,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: () => _switchMode(true),
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                          decoration: BoxDecoration(
                            color: _isCreativeMode.value
                                ? Theme.of(context).primaryColor
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.edit,
                                size: 16,
                                color: _isCreativeMode.value
                                    ? Colors.white
                                    : Theme.of(context).textTheme.bodyMedium?.color,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '创作模式',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  color: _isCreativeMode.value
                                      ? Colors.white
                                      : Theme.of(context).textTheme.bodyMedium?.color,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }



  Widget _buildChatMessages() {
    return Obx(() {
      final session = _smartComposerController.currentSession.value;
      if (session == null || session.messages.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.auto_fix_high,
                size: 48,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                '岱宗AI辅助助手',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '我可以帮助您：\n• 生成章节修改建议\n• 优化文字表达\n• 完善情节发展\n• 提供创作灵感',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.grey[500],
                  fontSize: 12,
                ),
              ),
            ],
          ),
        );
      }

      return ListView.builder(
        controller: _chatScrollController,
        padding: const EdgeInsets.all(16),
        itemCount: session.messages.length,
        itemBuilder: (context, index) {
          final message = session.messages[index];
          return _buildEnhancedMessageBubble(message);
        },
      );
    });
  }

  /// 构建增强的消息气泡
  Widget _buildEnhancedMessageBubble(ChatMessage message) {
    return EnhancedChatMessageWidget(
      message: message,
      originalContent: _originalChapterContent.value,
      onModificationStatusChanged: (messageId, modificationId, status) {
        _smartComposerController.updateModificationStatus(messageId, modificationId, status);
      },
      onBatchStatusChanged: (messageId, status) {
        _smartComposerController.updateBatchModificationStatus(messageId, status);
      },
      onApplyModifications: (messageId) {
        _applyModifications(messageId);
      },
    );
  }

  Widget _buildMessageBubble(ChatMessage message) {
    final isUser = message.role == 'user';

    // 检查是否是修改建议消息
    final isSuggestionMessage = !isUser && _isSuggestionMessage(message.content);

    if (isSuggestionMessage) {
      return _buildSuggestionMessageBubble(message);
    }

    return Align(
      alignment: isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(12),
        constraints: BoxConstraints(
          maxWidth: _aiPanelWidth * 0.8,
        ),
        decoration: BoxDecoration(
          color: isUser
              ? Theme.of(context).primaryColor
              : Theme.of(context).colorScheme.surfaceVariant,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          message.content,
          style: TextStyle(
            color: isUser ? Colors.white : null,
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  bool _isSuggestionMessage(String content) {
    // 检查消息内容是否包含修改建议或修改结果的JSON格式
    return (content.contains('"suggestions"') && content.contains('"original_text"')) ||
           (content.contains('"modifications"') && content.contains('"modified_text"'));
  }

  Widget _buildSuggestionMessageBubble(ChatMessage message) {
    // 检查是否是创作模式的修改消息
    if (message.content.contains('"modifications"')) {
      return _buildModificationMessageBubble(message);
    } else {
      return _buildSuggestionOnlyMessageBubble(message);
    }
  }

  Widget _buildSuggestionOnlyMessageBubble(ChatMessage message) {
    final suggestions = _parseEditSuggestionsJson(message.content);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      constraints: BoxConstraints(
        maxWidth: _aiPanelWidth * 0.95,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).primaryColor.withOpacity(0.3),
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '修改建议',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (suggestions.isNotEmpty)
            ...suggestions.map((suggestion) => _buildSuggestionCard(suggestion)).toList()
          else
            const Text('未能解析修改建议，请重试。'),
        ],
      ),
    );
  }

  Widget _buildModificationMessageBubble(ChatMessage message) {
    final messageId = message.id;
    List<TextModification> modifications = [];

    // 首先检查缓存
    if (_parsedModificationsCache.containsKey(messageId)) {
      modifications = _parsedModificationsCache[messageId]!;
    } else if (_pendingModifications.isNotEmpty &&
        (message.content.contains('测试修改') || message.content.contains('简单指令'))) {
      // 如果是测试修改或简单指令，直接使用待处理的修改
      modifications = _pendingModifications.toList();
      _parsedModificationsCache[messageId] = modifications;
    } else if (_isSuggestionMessage(message.content)) {
      // 尝试解析修改，但不在构建过程中修改状态
      try {
        modifications = _parseModificationsJsonSync(message.content);
        _parsedModificationsCache[messageId] = modifications;

        // 延迟添加到待处理列表
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _pendingModifications.addAll(modifications);
        });
      } catch (e) {
        print('同步解析修改失败: $e');
        modifications = [
          TextModification(
            id: 'error-${message.id}',
            originalText: '解析失败',
            modifiedText: '请查看原始响应',
            reason: '无法解析AI响应格式',
            startIndex: 0,
            endIndex: 0,
            modificationType: '错误',
            timestamp: DateTime.now(),
          )
        ];
        _parsedModificationsCache[messageId] = modifications;
      }
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      constraints: BoxConstraints(
        maxWidth: _aiPanelWidth * 0.95,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.orange.withOpacity(0.5),
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.edit,
                color: Colors.orange,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '内容修改',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (modifications.isNotEmpty) ...[
            Text(
              '检测到 ${modifications.length} 处修改：',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            ...modifications.map((modification) => _buildModificationCard(modification)).toList(),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: _rejectAllModifications,
                  style: TextButton.styleFrom(
                    backgroundColor: Colors.red.withOpacity(0.1),
                    foregroundColor: Colors.red,
                  ),
                  child: const Text('全部拒绝'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _acceptAllModifications,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('全部接受'),
                ),
              ],
            ),
          ] else
            const Text('未能解析修改内容，请重试。'),
        ],
      ),
    );
  }

  Widget _buildModificationCard(TextModification modification) {
    // 检查当前章节内容中是否包含原文
    final currentContent = _chapterControllers[_currentChapterIndex].text;
    final isTextFound = currentContent.contains(modification.originalText);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isTextFound ? Theme.of(context).dividerColor : Colors.red,
          width: isTextFound ? 1 : 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  modification.modificationType,
                  style: const TextStyle(fontSize: 10, fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(width: 8),
              if (!isTextFound)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.red.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    '文本未找到',
                    style: TextStyle(fontSize: 9, color: Colors.red, fontWeight: FontWeight.bold),
                  ),
                ),
              const Spacer(),
              Row(
                children: [
                  IconButton(
                    onPressed: () => _rejectSingleModification(modification),
                    icon: const Icon(Icons.close, size: 16),
                    style: IconButton.styleFrom(
                      backgroundColor: Colors.red.withOpacity(0.1),
                      foregroundColor: Colors.red,
                      minimumSize: const Size(32, 32),
                    ),
                  ),
                  const SizedBox(width: 4),
                  IconButton(
                    onPressed: isTextFound ? () => _acceptSingleModification(modification) : null,
                    icon: const Icon(Icons.check, size: 16),
                    style: IconButton.styleFrom(
                      backgroundColor: isTextFound ? Colors.green.withOpacity(0.1) : Colors.grey.withOpacity(0.1),
                      foregroundColor: isTextFound ? Colors.green : Colors.grey,
                      minimumSize: const Size(32, 32),
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            modification.reason,
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
          ),
          if (!isTextFound) ...[
            const SizedBox(height: 4),
            Text(
              '警告：在当前章节中找不到此原文，应用修改可能会失败。',
              style: TextStyle(fontSize: 10, color: Colors.red[700]),
            ),
          ],
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isTextFound ? Colors.red.withOpacity(0.1) : Colors.red.withOpacity(0.2),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Text(
                      '原文:',
                      style: TextStyle(fontSize: 10, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '(${modification.originalText.length} 字符)',
                      style: const TextStyle(fontSize: 9, color: Colors.grey),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  modification.originalText,
                  style: const TextStyle(fontSize: 11),
                ),
              ],
            ),
          ),
          const SizedBox(height: 4),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Text(
                      '修改后:',
                      style: TextStyle(fontSize: 10, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '(${modification.modifiedText.length} 字符)',
                      style: const TextStyle(fontSize: 9, color: Colors.grey),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  modification.modifiedText.isEmpty ? '(删除)' : modification.modifiedText,
                  style: TextStyle(
                    fontSize: 11,
                    fontStyle: modification.modifiedText.isEmpty ? FontStyle.italic : FontStyle.normal,
                    color: modification.modifiedText.isEmpty ? Colors.grey : null,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSuggestionCard(ChapterEditSuggestion suggestion) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).dividerColor,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            suggestion.reason,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 13,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '原文:',
                  style: TextStyle(fontSize: 11, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                Text(
                  suggestion.originalText,
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '建议修改:',
                  style: TextStyle(fontSize: 11, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                Text(
                  suggestion.suggestedText,
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () => _rejectSuggestionInMessage(suggestion),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                ),
                child: const Text('拒绝', style: TextStyle(fontSize: 12)),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: () => _applySuggestionInMessage(suggestion),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                ),
                child: const Text('应用', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildChatInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Column(
        children: [
          // 引用章节显示
          Obx(() {
            if (_referencedChapters.isEmpty) return const SizedBox.shrink();

            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.link, size: 16, color: Colors.blue[700]),
                      const SizedBox(width: 4),
                      Text(
                        '引用章节：',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[700],
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => _referencedChapters.clear(),
                        icon: const Icon(Icons.close, size: 16),
                        style: IconButton.styleFrom(
                          minimumSize: const Size(24, 24),
                          padding: EdgeInsets.zero,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Wrap(
                    spacing: 8,
                    runSpacing: 4,
                    children: _referencedChapters.map((chapter) => Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.blue.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '第${chapter.number}章：${chapter.title}',
                        style: const TextStyle(fontSize: 11),
                      ),
                    )).toList(),
                  ),
                ],
              ),
            );
          }),
          // 模式相关按钮行
          Obx(() => Row(
                children: [
                  if (!_isCreativeMode.value) ...[
                    // 聊天模式：生成建议按钮
                    ElevatedButton.icon(
                      onPressed: _isGeneratingSuggestions.value ? null : _generateEditSuggestions,
                      icon: _isGeneratingSuggestions.value
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.lightbulb_outline, size: 16),
                      label: Text(
                        _isGeneratingSuggestions.value ? '生成中...' : '生成修改建议',
                        style: const TextStyle(fontSize: 12),
                      ),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        minimumSize: const Size(0, 32),
                      ),
                    ),
                  ] else ...[
                    // 创作模式：测试按钮
                    ElevatedButton.icon(
                      onPressed: _createTestModification,
                      icon: const Icon(Icons.science, size: 16),
                      label: const Text('测试修改', style: TextStyle(fontSize: 12)),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.purple,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        minimumSize: const Size(0, 32),
                      ),
                    ),
                    const SizedBox(width: 8),
                    // 创作模式：修改操作按钮
                    if (_pendingModifications.isNotEmpty) ...[
                      ElevatedButton.icon(
                        onPressed: _acceptAllModifications,
                        icon: const Icon(Icons.check_circle, size: 16),
                        label: const Text('全部接受', style: TextStyle(fontSize: 12)),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          minimumSize: const Size(0, 32),
                        ),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton.icon(
                        onPressed: _rejectAllModifications,
                        icon: const Icon(Icons.cancel, size: 16),
                        label: const Text('全部拒绝', style: TextStyle(fontSize: 12)),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          minimumSize: const Size(0, 32),
                        ),
                      ),
                    ],
                  ],
                  const Spacer(),
                ],
              )),
          const SizedBox(height: 12),
          // 输入框行
          Row(
            children: [
              Expanded(
                child: Obx(() => TextField(
                      controller: _messageController,
                      maxLines: null,
                      decoration: InputDecoration(
                        hintText: _isCreativeMode.value
                            ? '输入编辑指令，如"删除环境描写"、"优化对话"等... 使用@第X章引用其他章节'
                            : '询问岱宗AI助手... 使用@第X章引用其他章节',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                      ),
                      onSubmitted: (_) => _sendMessage(),
                    )),
              ),
              const SizedBox(width: 8),
              Obx(() => IconButton(
                    onPressed: _smartComposerController.isLoading.value ? null : _sendMessage,
                    icon: _smartComposerController.isLoading.value
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.send),
                  )),
            ],
          ),
        ],
      ),
    );
  }

  // 模式切换方法
  void _switchMode(bool isCreativeMode) {
    _isCreativeMode.value = isCreativeMode;

    if (isCreativeMode) {
      // 切换到创作模式时，保存当前章节内容作为原始内容
      if (_currentChapterIndex < _chapterControllers.length) {
        final chapterContent = _chapterControllers[_currentChapterIndex].text;
        _originalChapterContent.value = chapterContent;
        // 同步到Smart Composer控制器
        _smartComposerController.switchCreativeMode(true, chapterContent: chapterContent);
      }
      Get.snackbar('模式切换', '已切换到创作模式，AI可以直接修改章节内容');
    } else {
      // 切换到聊天模式时，清空待处理的修改
      _pendingModifications.clear();
      _smartComposerController.switchCreativeMode(false);
      Get.snackbar('模式切换', '已切换到聊天模式，AI仅提供建议');
    }
  }

  // 核心功能方法
  void _switchToChapter(int index) {
    setState(() {
      _currentChapterIndex = index;
    });

    // 更新AI会话上下文
    if (_currentSession != null) {
      _currentSession = _currentSession!.copyWith(
        chapterNumber: index + 1,
        context: {
          ..._currentSession!.context ?? {},
          'current_chapter': index + 1,
        },
      );
    }


  }

  void _sendMessage() {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    // 解析章节引用
    final parsedMessage = _parseChapterReferences(text);
    final cleanedText = parsedMessage['text'] as String;
    final referencedChapters = parsedMessage['chapters'] as List<Chapter>;

    _messageController.clear();

    // 获取当前章节作为上下文
    Chapter? currentChapter;
    if (_currentNovel.chapters.isNotEmpty && _currentChapterIndex < _currentNovel.chapters.length) {
      currentChapter = _currentNovel.chapters[_currentChapterIndex];
    }

    // 更新引用的章节列表
    _referencedChapters.value = referencedChapters;

    if (_isCreativeMode.value) {
      // 创作模式：处理编辑指令
      _handleCreativeModeMessage(cleanedText, currentChapter, referencedChapters);
    } else {
      // 聊天模式：正常发送消息
      _smartComposerController.sendMessage(
        content: cleanedText,
        novel: _currentNovel,
        chapter: currentChapter,
        referencedChapters: referencedChapters,
      );
    }

    // 滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_chatScrollController.hasClients) {
        _chatScrollController.animateTo(
          _chatScrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  /// 解析章节引用（@功能）
  Map<String, dynamic> _parseChapterReferences(String text) {
    final referencedChapters = <Chapter>[];
    String cleanedText = text;

    // 匹配 @第X章 或 @X 的模式
    final chapterPattern = RegExp(r'@(?:第)?(\d+)(?:章)?');
    final matches = chapterPattern.allMatches(text);

    for (final match in matches) {
      final chapterNumber = int.tryParse(match.group(1) ?? '');
      if (chapterNumber != null) {
        // 查找对应的章节
        final chapterIndex = _currentNovel.chapters.indexWhere(
          (c) => c.number == chapterNumber,
        );

        if (chapterIndex != -1) {
          final chapter = _currentNovel.chapters[chapterIndex];
          referencedChapters.add(chapter);
        }
      }

      // 从文本中移除引用标记
      cleanedText = cleanedText.replaceAll(match.group(0)!, '');
    }

    // 清理多余的空格
    cleanedText = cleanedText.replaceAll(RegExp(r'\s+'), ' ').trim();

    return {
      'text': cleanedText,
      'chapters': referencedChapters,
    };
  }

  void _handleCreativeModeMessage(String instruction, Chapter? currentChapter, [List<Chapter>? referencedChapters]) async {
    if (currentChapter == null) {
      Get.snackbar('错误', '当前章节不存在');
      return;
    }

    final chapterContent = _chapterControllers[_currentChapterIndex].text;
    if (chapterContent.trim().isEmpty) {
      Get.snackbar('提示', '当前章节内容为空，无法执行编辑指令');
      return;
    }

    // 保存原始内容
    _originalChapterContent.value = chapterContent;

    try {
      // 先尝试简化的指令处理
      if (_trySimpleInstruction(instruction, chapterContent)) {
        _createSimpleInstructionMessage(instruction);
        return;
      }

      // 构建创作模式的提示词
      final prompt = _buildCreativeModePrompt(instruction, currentChapter, chapterContent, referencedChapters);

      // 发送到AI服务
      await _smartComposerController.sendMessage(
        content: prompt,
        novel: _currentNovel,
        chapter: currentChapter,
        referencedChapters: referencedChapters,
      );

    } catch (e) {
      Get.snackbar('错误', '执行编辑指令失败: $e');
    }
  }

  void _createSimpleInstructionMessage(String instruction) async {
    // 为简单指令创建聊天消息
    if (_currentSession != null) {
      final userMessage = ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        role: 'user',
        content: instruction,
        timestamp: DateTime.now(),
      );

      final assistantMessage = ChatMessage(
        id: (DateTime.now().millisecondsSinceEpoch + 1).toString(),
        role: 'assistant',
        content: '简单指令处理完成',
        timestamp: DateTime.now(),
      );

      final updatedMessages = [
        ..._currentSession!.messages,
        userMessage,
        assistantMessage,
      ];

      _currentSession = _currentSession!.copyWith(
        messages: updatedMessages,
        updatedAt: DateTime.now(),
      );

      _smartComposerController.currentSession.value = _currentSession;
    }
  }

  bool _trySimpleInstruction(String instruction, String content) {
    final lowerInstruction = instruction.toLowerCase();

    // 处理续写指令
    if (lowerInstruction.contains('续写') || lowerInstruction.contains('继续写') || lowerInstruction.contains('添加内容')) {
      _handleSimpleContinue(content, instruction);
      return true;
    }

    // 处理删除指令
    if (lowerInstruction.contains('删除')) {
      if (lowerInstruction.contains('环境描写') || lowerInstruction.contains('景物描写')) {
        _handleSimpleDelete(content, '环境描写');
        return true;
      }
      if (lowerInstruction.contains('对话') || lowerInstruction.contains('对白')) {
        _handleSimpleDelete(content, '对话');
        return true;
      }
      if (lowerInstruction.contains('心理描写') || lowerInstruction.contains('内心')) {
        _handleSimpleDelete(content, '心理描写');
        return true;
      }
      if (lowerInstruction.contains('动作描写') || lowerInstruction.contains('动作')) {
        _handleSimpleDelete(content, '动作描写');
        return true;
      }
    }

    // 处理优化指令
    if (lowerInstruction.contains('优化') || lowerInstruction.contains('改善')) {
      _handleSimpleOptimize(content, instruction);
      return true;
    }

    return false;
  }

  void _handleSimpleDelete(String content, String target) {
    // 尝试智能识别要删除的内容
    List<String> candidatesForDeletion = [];

    if (target.contains('环境描写')) {
      // 查找可能的环境描写句子
      final envPatterns = [
        RegExp(r'[。！？][^。！？]*?(天空|阳光|微风|树木|花草|山峦|河流|建筑)[^。！？]*?[。！？]'),
        RegExp(r'[。！？][^。！？]*?(明媚|清新|幽静|宁静|美丽|壮观)[^。！？]*?[。！？]'),
      ];
      for (final pattern in envPatterns) {
        final matches = pattern.allMatches(content);
        for (final match in matches) {
          candidatesForDeletion.add(match.group(0)?.substring(1) ?? '');
        }
      }
    } else if (target.contains('对话')) {
      // 查找对话内容
      final dialogPatterns = [
        RegExp(r'"[^"]*"'),
        RegExp(r'"[^"]*"'),
        RegExp(r'「[^」]*」'),
      ];
      for (final pattern in dialogPatterns) {
        final matches = pattern.allMatches(content);
        for (final match in matches) {
          candidatesForDeletion.add(match.group(0) ?? '');
        }
      }
    }

    // 创建修改建议
    if (candidatesForDeletion.isNotEmpty) {
      for (int i = 0; i < candidatesForDeletion.length && i < 3; i++) {
        final candidate = candidatesForDeletion[i].trim();
        if (candidate.isNotEmpty && content.contains(candidate)) {
          final modification = TextModification(
            id: DateTime.now().millisecondsSinceEpoch.toString() + '_$i',
            originalText: candidate,
            modifiedText: '',
            reason: '智能识别的$target内容',
            startIndex: content.indexOf(candidate),
            endIndex: content.indexOf(candidate) + candidate.length,
            modificationType: '删除',
            timestamp: DateTime.now(),
          );
          _pendingModifications.add(modification);
        }
      }
      Get.snackbar('智能识别', '已识别 ${candidatesForDeletion.length} 处可能的$target，请在聊天中查看');
    } else {
      // 创建一个通用的删除建议
      final modification = TextModification(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        originalText: '（请手动选择要删除的$target部分）',
        modifiedText: '',
        reason: '根据用户指令删除$target',
        startIndex: 0,
        endIndex: 0,
        modificationType: '删除',
        timestamp: DateTime.now(),
      );
      _pendingModifications.add(modification);
      Get.snackbar('处理完成', '已生成删除$target的建议，请手动选择具体内容');
    }
  }

  void _handleSimpleContinue(String content, String instruction) {
    // 创建续写建议
    final modification = TextModification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      originalText: '',
      modifiedText: '\n\n（这里是AI续写的内容，请使用AI生成具体内容）',
      reason: '根据用户指令进行续写',
      startIndex: content.length,
      endIndex: content.length,
      modificationType: '续写',
      timestamp: DateTime.now(),
    );

    _pendingModifications.add(modification);
    Get.snackbar('续写建议', '已创建续写建议，建议使用AI生成具体内容');
  }

  void _handleSimpleOptimize(String content, String instruction) {
    // 找到第一个段落进行优化示例
    final paragraphs = content.split('\n').where((p) => p.trim().isNotEmpty).toList();
    if (paragraphs.isNotEmpty) {
      final firstParagraph = paragraphs.first.trim();
      final modification = TextModification(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        originalText: firstParagraph,
        modifiedText: '（优化后的文字，请使用AI生成）',
        reason: '根据用户指令优化文字表达',
        startIndex: content.indexOf(firstParagraph),
        endIndex: content.indexOf(firstParagraph) + firstParagraph.length,
        modificationType: '优化',
        timestamp: DateTime.now(),
      );
      _pendingModifications.add(modification);
      Get.snackbar('优化建议', '已创建优化建议，建议使用AI生成具体内容');
    }
  }

  void _createTestModification() async {
    final content = _chapterControllers[_currentChapterIndex].text;
    if (content.trim().isEmpty) {
      Get.snackbar('提示', '当前章节内容为空');
      return;
    }

    // 找到第一个句子或段落作为测试目标
    String originalText;
    int endIndex;

    // 尝试找到第一个句号、问号或感叹号结束的句子
    final sentenceEnd = RegExp(r'[。！？.!?]').firstMatch(content);
    if (sentenceEnd != null && sentenceEnd.end <= 100) {
      originalText = content.substring(0, sentenceEnd.end);
      endIndex = sentenceEnd.end;
    } else if (content.length > 30) {
      // 如果没有找到句子结束符，取前30个字符
      originalText = content.substring(0, 30);
      endIndex = 30;
    } else {
      // 如果内容很短，取全部内容
      originalText = content;
      endIndex = content.length;
    }

    // 创建一个测试修改
    final testModification = TextModification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      originalText: originalText,
      modifiedText: '【这是测试修改后的文本】',
      reason: '这是创作模式的测试修改，用于验证功能是否正常工作。',
      startIndex: 0,
      endIndex: endIndex,
      modificationType: '测试',
      timestamp: DateTime.now(),
    );

    _pendingModifications.add(testModification);

    // 模拟AI响应，在聊天中显示修改
    final testResponse = '''
{
  "modifications": [
    {
      "original_text": "${testModification.originalText}",
      "modified_text": "${testModification.modifiedText}",
      "reason": "${testModification.reason}",
      "start_index": ${testModification.startIndex},
      "end_index": ${testModification.endIndex},
      "modification_type": "${testModification.modificationType}"
    }
  ]
}
''';

    // 添加到聊天记录
    if (_currentSession != null) {
      final userMessage = ChatMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        role: 'user',
        content: '测试创作模式修改功能',
        timestamp: DateTime.now(),
      );

      final assistantMessage = ChatMessage(
        id: (DateTime.now().millisecondsSinceEpoch + 1).toString(),
        role: 'assistant',
        content: testResponse,
        timestamp: DateTime.now(),
      );

      final updatedMessages = [
        ..._currentSession!.messages,
        userMessage,
        assistantMessage,
      ];

      _currentSession = _currentSession!.copyWith(
        messages: updatedMessages,
        updatedAt: DateTime.now(),
      );

      // 更新SmartComposerController的当前会话
      _smartComposerController.currentSession.value = _currentSession;
    }

    Get.snackbar('测试完成', '已创建测试修改，请在聊天中查看并测试接受/拒绝功能');
  }

  void _saveChanges() async {
    // 更新小说标题
    _currentNovel.title = _titleController.text;

    // 更新章节内容
    for (int i = 0; i < _chapterControllers.length && i < _currentNovel.chapters.length; i++) {
      _currentNovel.chapters[i].content = _chapterControllers[i].text;
    }

    // 保存到控制器
    await _novelController.saveNovel(_currentNovel);

    setState(() {
      _hasChanges = false;
    });

    Get.snackbar('成功', '小说已保存');
  }

  // 生成修改建议的核心方法
  void _generateEditSuggestions() async {
    if (_currentChapterIndex >= _currentNovel.chapters.length) {
      Get.snackbar('错误', '当前章节不存在');
      return;
    }

    final currentChapter = _currentNovel.chapters[_currentChapterIndex];
    final chapterContent = _chapterControllers[_currentChapterIndex].text;

    if (chapterContent.trim().isEmpty) {
      Get.snackbar('提示', '当前章节内容为空，无法生成修改建议');
      return;
    }

    _isGeneratingSuggestions.value = true;

    try {
      // 构建专门的修改建议提示
      final prompt = _buildEditSuggestionPrompt(currentChapter, chapterContent);

      // 发送到AI服务获取建议
      await _smartComposerController.sendMessage(
        content: prompt,
        novel: _currentNovel,
        chapter: currentChapter,
      );

      // 滚动到底部显示新的建议消息
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_chatScrollController.hasClients) {
          _chatScrollController.animateTo(
            _chatScrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });

    } catch (e) {
      Get.snackbar('错误', '生成修改建议失败: $e');
    } finally {
      _isGeneratingSuggestions.value = false;
    }
  }

  String _buildEditSuggestionPrompt(Chapter chapter, String content) {
  /// 应用修改建议到章节内容
  void _applyModifications(String messageId) {
    try {
      final modifiedContent = _smartComposerController.applyAcceptedModifications(messageId);

      // 更新当前章节的文本控制器
      if (_currentChapterIndex < _chapterControllers.length) {
        _chapterControllers[_currentChapterIndex].text = modifiedContent;

        // 保存章节内容
        _saveChapterContent(_currentChapterIndex);

        // 更新原始内容为新的内容
        _originalChapterContent.value = modifiedContent;
        _smartComposerController.originalChapterContent.value = modifiedContent;

        Get.snackbar(
          '修改已应用',
          '章节内容已更新，修改建议已应用到第${_currentChapterIndex + 1}章',
          backgroundColor: Colors.green.withOpacity(0.8),
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar('错误', '应用修改失败: $e');
    }
  }

    return '''请作为专业的文学编辑，分析以下章节内容并提供具体的修改建议。

小说信息：
- 标题：${_currentNovel.title}
- 类型：${_currentNovel.genre}
- 大纲：${_currentNovel.outline}

当前章节：
- 标题：${chapter.title}
- 内容：
$content

请提供3-5个具体的修改建议，每个建议应该：
1. 指出需要修改的具体文字段落
2. 提供改进后的文字
3. 说明修改的理由

请以以下JSON格式返回建议：
{
  "suggestions": [
    {
      "original_text": "需要修改的原文",
      "suggested_text": "建议的修改文字",
      "reason": "修改理由",
      "start_index": 原文在章节中的起始位置,
      "end_index": 原文在章节中的结束位置
    }
  ]
}

重要：只返回JSON格式，不要包含其他文字。''';
  }

  String _buildCreativeModePrompt(String instruction, Chapter chapter, String content, [List<Chapter>? referencedChapters]) {
    // 构建引用章节内容
    String referencedContent = '';
    if (referencedChapters != null && referencedChapters.isNotEmpty) {
      final buffer = StringBuffer();
      for (final refChapter in referencedChapters) {
        buffer.writeln('## 第${refChapter.number}章：${refChapter.title}');
        buffer.writeln(refChapter.content);
        buffer.writeln();
      }
      referencedContent = buffer.toString();
    }

    // 使用新的创作模式提示词模板
    return NovelPromptTemplates.creativeEditTemplate.format({
      'novelTitle': _currentNovel?.title ?? '未知小说',
      'chapterNumber': chapter.number.toString(),
      'chapterTitle': chapter.title,
      'userInstruction': instruction,
      'originalContent': content,
      'referencedContent': referencedContent.isNotEmpty ? referencedContent : '无引用章节',
    });
  }

  String _buildContinueWritingPrompt(String instruction, Chapter chapter, String content, [List<Chapter>? referencedChapters]) {
    final buffer = StringBuffer();

    buffer.writeln('你是专业的小说作家。用户希望你续写章节内容。');
    buffer.writeln();
    buffer.writeln('当前章节：第${chapter.number}章 - ${chapter.title}');
    buffer.writeln('当前章节内容（用于精确匹配）：');
    buffer.writeln('"""');
    buffer.writeln(content);
    buffer.writeln('"""');
    buffer.writeln();
    buffer.writeln('用户指令：$instruction');
    buffer.writeln();
    buffer.writeln('注意：引用章节和小说背景信息通过memory参数传递，请参考memory中的信息。');
    buffer.writeln();
    buffer.writeln('请在章节末尾续写内容。返回JSON格式：');
    buffer.writeln();
    buffer.writeln('```json');
    buffer.writeln('{');
    buffer.writeln('  "modifications": [');
    buffer.writeln('    {');
    buffer.writeln('      "original_text": "",');
    buffer.writeln('      "modified_text": "你续写的新内容",');
    buffer.writeln('      "reason": "根据用户指令进行续写",');
    buffer.writeln('      "start_index": ${content.length},');
    buffer.writeln('      "end_index": ${content.length},');
    buffer.writeln('      "modification_type": "续写"');
    buffer.writeln('    }');
    buffer.writeln('  ]');
    buffer.writeln('}');
    buffer.writeln('```');
    buffer.writeln();
    buffer.writeln('要求：');
    buffer.writeln('1. 只返回JSON，不要其他文字');
    buffer.writeln('2. 续写内容要与现有内容风格一致');
    buffer.writeln('3. modified_text包含你要添加的新内容');
    buffer.writeln('4. original_text保持为空字符串""');
    if (referencedChapters != null && referencedChapters.isNotEmpty) {
      buffer.writeln('5. 参考memory中引用章节的内容和风格');
    }

    return buffer.toString();
  }

  String _buildDeletePrompt(String instruction, Chapter chapter, String content, [List<Chapter>? referencedChapters]) {
    final buffer = StringBuffer();

    buffer.writeln('你是专业的文学编辑。用户希望你删除章节中的特定内容。');
    buffer.writeln();
    buffer.writeln('当前章节：第${chapter.number}章 - ${chapter.title}');
    buffer.writeln('当前章节内容（用于精确匹配）：');
    buffer.writeln('"""');
    buffer.writeln(content);
    buffer.writeln('"""');
    buffer.writeln();
    buffer.writeln('用户指令：$instruction');
    buffer.writeln();
    buffer.writeln('注意：引用章节和小说背景信息通过memory参数传递，请参考memory中的信息。');
    buffer.writeln();
    buffer.writeln('请找到需要删除的内容并返回JSON格式：');
    buffer.writeln();
    buffer.writeln('```json');
    buffer.writeln('{');
    buffer.writeln('  "modifications": [');
    buffer.writeln('    {');
    buffer.writeln('      "original_text": "要删除的具体文字（必须完全匹配上面的章节内容）",');
    buffer.writeln('      "modified_text": "",');
    buffer.writeln('      "reason": "根据用户指令删除此内容",');
    buffer.writeln('      "start_index": 0,');
    buffer.writeln('      "end_index": 10,');
    buffer.writeln('      "modification_type": "删除"');
    buffer.writeln('    }');
    buffer.writeln('  ]');
    buffer.writeln('}');
    buffer.writeln('```');
    buffer.writeln();
    buffer.writeln('要求：');
    buffer.writeln('1. 只返回JSON，不要其他文字');
    buffer.writeln('2. original_text必须是上面章节内容中确实存在的文字，一字不差');
    buffer.writeln('3. modified_text设为空字符串""');
    buffer.writeln('4. 可以返回多个删除操作');
    buffer.writeln('5. 选择完整的句子或段落进行删除，避免截断');
    if (referencedChapters != null && referencedChapters.isNotEmpty) {
      buffer.writeln('6. 参考memory中引用章节来理解删除的上下文');
    }

    return buffer.toString();
  }

  String _buildGeneralEditPrompt(String instruction, Chapter chapter, String content, [List<Chapter>? referencedChapters]) {
    final buffer = StringBuffer();

    buffer.writeln('你是专业的文学编辑。请根据用户指令修改章节内容。');
    buffer.writeln();
    buffer.writeln('当前章节：第${chapter.number}章 - ${chapter.title}');
    buffer.writeln('当前章节内容（用于精确匹配）：');
    buffer.writeln('"""');
    buffer.writeln(content);
    buffer.writeln('"""');
    buffer.writeln();
    buffer.writeln('用户指令：$instruction');
    buffer.writeln();
    buffer.writeln('注意：引用章节和小说背景信息通过memory参数传递，请参考memory中的信息。');
    buffer.writeln();
    buffer.writeln('请返回JSON格式的修改结果：');
    buffer.writeln();
    buffer.writeln('```json');
    buffer.writeln('{');
    buffer.writeln('  "modifications": [');
    buffer.writeln('    {');
    buffer.writeln('      "original_text": "需要修改的原文（必须完全匹配上面的章节内容）",');
    buffer.writeln('      "modified_text": "修改后的文字",');
    buffer.writeln('      "reason": "修改理由",');
    buffer.writeln('      "start_index": 0,');
    buffer.writeln('      "end_index": 10,');
    buffer.writeln('      "modification_type": "优化"');
    buffer.writeln('    }');
    buffer.writeln('  ]');
    buffer.writeln('}');
    buffer.writeln('```');
    buffer.writeln();
    buffer.writeln('要求：');
    buffer.writeln('1. 只返回JSON，不要其他文字');
    buffer.writeln('2. original_text必须完全匹配上面章节内容中的文字，一字不差');
    buffer.writeln('3. 选择完整的句子或段落进行修改，避免截断');
    buffer.writeln('4. 可以返回多个修改操作');
    buffer.writeln('5. 确保修改后的内容风格与原文一致');
    if (referencedChapters != null && referencedChapters.isNotEmpty) {
      buffer.writeln('6. 参考memory中引用章节的风格和内容进行修改');
    }

    return buffer.toString();
  }

  // 在消息中应用建议
  void _applySuggestionInMessage(ChapterEditSuggestion suggestion) {
    final controller = _chapterControllers[_currentChapterIndex];
    final currentText = controller.text;

    // 查找并替换文本
    final newText = currentText.replaceFirst(
      suggestion.originalText,
      suggestion.suggestedText,
    );

    if (newText != currentText) {
      controller.text = newText;
      _onTextChanged();
      Get.snackbar('成功', '修改建议已应用');
    } else {
      Get.snackbar('错误', '未找到要替换的文本');
    }
  }

  // 在消息中拒绝建议
  void _rejectSuggestionInMessage(ChapterEditSuggestion suggestion) {
    Get.snackbar('已拒绝', '修改建议已被拒绝');
  }

  // 接受所有修改
  void _acceptAllModifications() {
    if (_pendingModifications.isEmpty) return;

    final controller = _chapterControllers[_currentChapterIndex];
    String newContent = controller.text;
    int appliedCount = 0;
    int failedCount = 0;

    // 创建修改的副本，避免在迭代过程中修改列表
    final modificationsToApply = _pendingModifications.toList();

    for (final modification in modificationsToApply) {
      // 优先使用文本匹配
      if (modification.originalText.isNotEmpty && newContent.contains(modification.originalText)) {
        final beforeReplace = newContent;
        newContent = newContent.replaceFirst(
          modification.originalText,
          modification.modifiedText,
        );

        if (newContent != beforeReplace) {
          appliedCount++;
          print('批量应用修改: "${modification.originalText}" -> "${modification.modifiedText}"');
        } else {
          failedCount++;
          print('批量应用失败: 文本替换无效');
        }
      } else {
        failedCount++;
        print('批量应用失败: 找不到目标文本 "${modification.originalText}"');
      }
    }

    if (appliedCount > 0) {
      controller.text = newContent;
      _onTextChanged();
    }

    _pendingModifications.clear();

    if (failedCount > 0) {
      Get.snackbar('部分成功', '已应用 $appliedCount 个修改，$failedCount 个修改失败');
    } else {
      Get.snackbar('成功', '所有 $appliedCount 个修改已应用');
    }
  }

  // 拒绝所有修改
  void _rejectAllModifications() {
    if (_pendingModifications.isEmpty) return;

    // 恢复原始内容
    final controller = _chapterControllers[_currentChapterIndex];
    controller.text = _originalChapterContent.value;
    _pendingModifications.clear();
    Get.snackbar('已拒绝', '所有修改已被拒绝，内容已恢复');
  }

  // 接受单个修改
  void _acceptSingleModification(TextModification modification) {
    final controller = _chapterControllers[_currentChapterIndex];
    final currentText = controller.text;

    // 优先使用文本匹配，而不是依赖索引
    if (modification.originalText.isNotEmpty && currentText.contains(modification.originalText)) {
      // 使用文本替换，更安全
      final newText = currentText.replaceFirst(
        modification.originalText,
        modification.modifiedText,
      );

      if (newText != currentText) {
        controller.text = newText;
        _pendingModifications.remove(modification);
        _onTextChanged();
        Get.snackbar('成功', '修改已应用');
        print('应用修改: "${modification.originalText}" -> "${modification.modifiedText}"');
      } else {
        Get.snackbar('错误', '文本替换失败');
      }
    } else if (modification.startIndex >= 0 && modification.endIndex <= currentText.length) {
      // 备用方案：使用索引，但要验证文本内容
      final targetText = currentText.substring(modification.startIndex, modification.endIndex);

      if (targetText == modification.originalText || modification.originalText.isEmpty) {
        final newText = currentText.replaceRange(
          modification.startIndex,
          modification.endIndex,
          modification.modifiedText,
        );
        controller.text = newText;
        _pendingModifications.remove(modification);
        _onTextChanged();
        Get.snackbar('成功', '修改已应用');
        print('通过索引应用修改: 位置${modification.startIndex}-${modification.endIndex}');
      } else {
        Get.snackbar('错误', '目标文本不匹配，无法安全应用修改');
        print('文本不匹配: 期望"${modification.originalText}"，实际"$targetText"');
      }
    } else {
      Get.snackbar('错误', '修改位置无效或找不到目标文本');
    }
  }

  // 拒绝单个修改
  void _rejectSingleModification(TextModification modification) {
    _pendingModifications.remove(modification);
    Get.snackbar('已拒绝', '修改已被拒绝');
  }

  List<ChapterEditSuggestion> _parseEditSuggestionsJson(String response) {
    try {
      // 尝试从响应中提取JSON
      final jsonStart = response.indexOf('{');
      final jsonEnd = response.lastIndexOf('}');

      if (jsonStart == -1 || jsonEnd == -1) {
        return [];
      }

      final jsonStr = response.substring(jsonStart, jsonEnd + 1);
      final Map<String, dynamic> data = json.decode(jsonStr);

      final List<dynamic> suggestionsData = data['suggestions'] ?? [];
      final List<ChapterEditSuggestion> suggestions = [];

      for (final suggestionData in suggestionsData) {
        final suggestion = ChapterEditSuggestion(
          id: DateTime.now().millisecondsSinceEpoch.toString() + suggestions.length.toString(),
          originalText: suggestionData['original_text'] ?? '',
          suggestedText: suggestionData['suggested_text'] ?? '',
          reason: suggestionData['reason'] ?? '',
          startIndex: suggestionData['start_index'] ?? 0,
          endIndex: suggestionData['end_index'] ?? 0,
          timestamp: DateTime.now(),
        );
        suggestions.add(suggestion);
      }

      return suggestions;
    } catch (e) {
      print('解析修改建议JSON失败: $e');
      return [];
    }
  }

  List<TextModification> _parseModificationsJsonSync(String response) {
    try {
      print('同步解析AI响应: $response');

      // 尝试多种方式提取JSON
      String? jsonStr;

      // 方法1: 查找完整的JSON对象
      final jsonStart = response.indexOf('{');
      final jsonEnd = response.lastIndexOf('}');

      if (jsonStart != -1 && jsonEnd != -1 && jsonEnd > jsonStart) {
        jsonStr = response.substring(jsonStart, jsonEnd + 1);
      }

      // 方法2: 如果方法1失败，尝试查找```json代码块
      if (jsonStr == null) {
        final codeBlockStart = response.indexOf('```json');
        final codeBlockEnd = response.indexOf('```', codeBlockStart + 7);
        if (codeBlockStart != -1 && codeBlockEnd != -1) {
          jsonStr = response.substring(codeBlockStart + 7, codeBlockEnd).trim();
        }
      }

      // 方法3: 如果还是失败，尝试查找```代码块
      if (jsonStr == null) {
        final codeBlockStart = response.indexOf('```');
        final codeBlockEnd = response.indexOf('```', codeBlockStart + 3);
        if (codeBlockStart != -1 && codeBlockEnd != -1) {
          final blockContent = response.substring(codeBlockStart + 3, codeBlockEnd).trim();
          if (blockContent.startsWith('{')) {
            jsonStr = blockContent;
          }
        }
      }

      if (jsonStr == null || jsonStr.isEmpty) {
        print('无法从响应中提取JSON');
        return [];
      }

      print('提取的JSON: $jsonStr');

      // 尝试修复常见的JSON格式问题
      jsonStr = _fixJsonFormat(jsonStr);

      final Map<String, dynamic> data = json.decode(jsonStr);

      final List<dynamic> modificationsData = data['modifications'] ?? [];
      final List<TextModification> modifications = [];

      for (final modificationData in modificationsData) {
        final modification = TextModification(
          id: DateTime.now().millisecondsSinceEpoch.toString() + modifications.length.toString(),
          originalText: modificationData['original_text'] ?? '',
          modifiedText: modificationData['modified_text'] ?? '',
          reason: modificationData['reason'] ?? '',
          startIndex: modificationData['start_index'] ?? 0,
          endIndex: modificationData['end_index'] ?? 0,
          modificationType: modificationData['modification_type'] ?? '修改',
          timestamp: DateTime.now(),
        );
        modifications.add(modification);
      }

      return modifications;
    } catch (e) {
      print('同步解析修改JSON失败: $e');
      return [];
    }
  }

  List<TextModification> _parseModificationsJson(String response) {
    try {
      print('原始AI响应: $response');

      // 尝试多种方式提取JSON
      String? jsonStr;

      // 方法1: 查找完整的JSON对象
      final jsonStart = response.indexOf('{');
      final jsonEnd = response.lastIndexOf('}');

      if (jsonStart != -1 && jsonEnd != -1 && jsonEnd > jsonStart) {
        jsonStr = response.substring(jsonStart, jsonEnd + 1);
      }

      // 方法2: 如果方法1失败，尝试查找```json代码块
      if (jsonStr == null) {
        final codeBlockStart = response.indexOf('```json');
        final codeBlockEnd = response.indexOf('```', codeBlockStart + 7);
        if (codeBlockStart != -1 && codeBlockEnd != -1) {
          jsonStr = response.substring(codeBlockStart + 7, codeBlockEnd).trim();
        }
      }

      // 方法3: 如果还是失败，尝试查找```代码块
      if (jsonStr == null) {
        final codeBlockStart = response.indexOf('```');
        final codeBlockEnd = response.indexOf('```', codeBlockStart + 3);
        if (codeBlockStart != -1 && codeBlockEnd != -1) {
          final blockContent = response.substring(codeBlockStart + 3, codeBlockEnd).trim();
          if (blockContent.startsWith('{')) {
            jsonStr = blockContent;
          }
        }
      }

      if (jsonStr == null || jsonStr.isEmpty) {
        print('无法从响应中提取JSON');
        // 延迟显示错误对话框，避免在构建过程中调用
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _showFallbackModification(response);
        });
        return [];
      }

      print('提取的JSON: $jsonStr');

      // 尝试修复常见的JSON格式问题
      jsonStr = _fixJsonFormat(jsonStr);

      final Map<String, dynamic> data = json.decode(jsonStr);

      final List<dynamic> modificationsData = data['modifications'] ?? [];
      final List<TextModification> modifications = [];

      for (final modificationData in modificationsData) {
        final modification = TextModification(
          id: DateTime.now().millisecondsSinceEpoch.toString() + modifications.length.toString(),
          originalText: modificationData['original_text'] ?? '',
          modifiedText: modificationData['modified_text'] ?? '',
          reason: modificationData['reason'] ?? '',
          startIndex: modificationData['start_index'] ?? 0,
          endIndex: modificationData['end_index'] ?? 0,
          modificationType: modificationData['modification_type'] ?? '修改',
          timestamp: DateTime.now(),
        );
        modifications.add(modification);
      }

      // 直接添加修改到待处理列表
      _pendingModifications.addAll(modifications);

      return modifications;
    } catch (e) {
      print('解析修改JSON失败: $e');
      print('响应内容: $response');
      // 延迟显示错误对话框，避免在构建过程中调用
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showFallbackModification(response);
      });
      return [];
    }
  }

  String _fixJsonFormat(String jsonStr) {
    // 修复常见的JSON格式问题
    String fixed = jsonStr;

    // 移除可能的前后空白和换行
    fixed = fixed.trim();

    // 确保以{开始，}结束
    if (!fixed.startsWith('{')) {
      final start = fixed.indexOf('{');
      if (start != -1) {
        fixed = fixed.substring(start);
      }
    }

    if (!fixed.endsWith('}')) {
      final end = fixed.lastIndexOf('}');
      if (end != -1) {
        fixed = fixed.substring(0, end + 1);
      }
    }

    return fixed;
  }

  void _showFallbackModification(String response) {
    // 尝试从非JSON响应中提取有用信息
    final extractedContent = _extractContentFromResponse(response);

    if (extractedContent != null) {
      // 创建基于提取内容的修改建议
      final modification = TextModification(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        originalText: extractedContent['original'] ?? '',
        modifiedText: extractedContent['modified'] ?? '',
        reason: extractedContent['reason'] ?? 'AI建议的修改',
        startIndex: 0,
        endIndex: extractedContent['original']?.length ?? 0,
        modificationType: extractedContent['type'] ?? '修改',
        timestamp: DateTime.now(),
      );

      _pendingModifications.add(modification);
      Get.snackbar('智能解析', '已从AI响应中提取修改建议');
    } else {
      // 显示原始响应对话框
      Get.dialog(
        AlertDialog(
          title: const Text('AI响应需要手动处理'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('AI无法生成标准格式的修改建议，但提供了以下内容：'),
              const SizedBox(height: 12),
              Container(
                constraints: const BoxConstraints(maxHeight: 200),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    response,
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
              ),
              const SizedBox(height: 12),
              const Text(
                '您可以：\n1. 手动复制有用的内容\n2. 重新用更简单的指令\n3. 切换到聊天模式获取建议',
                style: TextStyle(fontSize: 11, color: Colors.grey),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('关闭'),
            ),
            TextButton(
              onPressed: () {
                Get.back();
                _switchMode(false); // 切换到聊天模式
                Get.snackbar('模式切换', '已切换到聊天模式，您可以获取AI建议');
              },
              child: const Text('切换到聊天模式'),
            ),
          ],
        ),
      );
    }
  }

  Map<String, String>? _extractContentFromResponse(String response) {
    // 尝试从自然语言响应中提取修改信息
    try {
      // 查找常见的修改模式
      if (response.contains('修改') || response.contains('改为') || response.contains('替换')) {
        // 尝试提取"将...修改为..."的模式
        final modifyPattern = RegExp(r'将["""]([^"""]+)["""].*?[修改改为替换成][为成]?["""]([^"""]+)["""]');
        final match = modifyPattern.firstMatch(response);
        if (match != null) {
          return {
            'original': match.group(1) ?? '',
            'modified': match.group(2) ?? '',
            'reason': '根据AI建议修改',
            'type': '修改',
          };
        }
      }

      if (response.contains('删除') || response.contains('去掉')) {
        // 尝试提取删除模式
        final deletePattern = RegExp(r'删除["""]([^"""]+)["""]');
        final match = deletePattern.firstMatch(response);
        if (match != null) {
          return {
            'original': match.group(1) ?? '',
            'modified': '',
            'reason': '根据AI建议删除',
            'type': '删除',
          };
        }
      }

      if (response.contains('添加') || response.contains('续写')) {
        // 尝试提取添加模式
        final addPattern = RegExp(r'添加["""]([^"""]+)["""]');
        final match = addPattern.firstMatch(response);
        if (match != null) {
          return {
            'original': '',
            'modified': match.group(1) ?? '',
            'reason': '根据AI建议添加',
            'type': '添加',
          };
        }
      }

      return null;
    } catch (e) {
      print('提取响应内容失败: $e');
      return null;
    }
  }



  @override
  void dispose() {
    _titleController.dispose();
    for (final controller in _chapterControllers) {
      controller.dispose();
    }
    _messageController.dispose();
    _chatScrollController.dispose();
    _editorScrollController.dispose();
    super.dispose();
  }
}

/// 章节编辑建议模型
class ChapterEditSuggestion {
  final String id;
  final String originalText;
  final String suggestedText;
  final String reason;
  final int startIndex;
  final int endIndex;
  final DateTime timestamp;
  bool isApplied;
  bool isRejected;

  ChapterEditSuggestion({
    required this.id,
    required this.originalText,
    required this.suggestedText,
    required this.reason,
    required this.startIndex,
    required this.endIndex,
    required this.timestamp,
    this.isApplied = false,
    this.isRejected = false,
  });
}

/// 文本修改模型（用于创作模式）
class TextModification {
  final String id;
  final String originalText;
  final String modifiedText;
  final String reason;
  final int startIndex;
  final int endIndex;
  final String modificationType;
  final DateTime timestamp;
  bool isApplied;
  bool isRejected;

  TextModification({
    required this.id,
    required this.originalText,
    required this.modifiedText,
    required this.reason,
    required this.startIndex,
    required this.endIndex,
    required this.modificationType,
    required this.timestamp,
    this.isApplied = false,
    this.isRejected = false,
  });
}
