import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/models/smart_composer_models.dart';
import 'package:novel_app/models/text_modification.dart';
import 'package:novel_app/widgets/diff_view_widget.dart';
import 'package:novel_app/controllers/smart_composer_controller.dart';

/// 增强的聊天消息组件 - 支持Diff视图和修改建议
class EnhancedChatMessageWidget extends StatelessWidget {
  final ChatMessage message;
  final String originalContent;
  final Function(String messageId, String modificationId, ModificationStatus status)? onModificationStatusChanged;
  final Function(String messageId, ModificationStatus status)? onBatchStatusChanged;
  final Function(String messageId)? onApplyModifications;

  const EnhancedChatMessageWidget({
    Key? key,
    required this.message,
    required this.originalContent,
    this.onModificationStatusChanged,
    this.onBatchStatusChanged,
    this.onApplyModifications,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isUser = message.role == 'user';
    
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) _buildAvatar(context, false),
          const SizedBox(width: 8),
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.8,
              ),
              decoration: BoxDecoration(
                color: isUser 
                    ? theme.colorScheme.primary
                    : theme.colorScheme.surfaceVariant,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildMessageHeader(context),
                  _buildMessageContent(context),
                  if (message.isCreativeEdit && message.editResponse != null)
                    _buildDiffView(context),
                  if (message.isCreativeEdit && message.hasPendingModifications)
                    _buildActionButtons(context),
                ],
              ),
            ),
          ),
          const SizedBox(width: 8),
          if (isUser) _buildAvatar(context, true),
        ],
      ),
    );
  }

  /// 构建头像
  Widget _buildAvatar(BuildContext context, bool isUser) {
    final theme = Theme.of(context);
    
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: isUser ? theme.colorScheme.primary : theme.colorScheme.secondary,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Icon(
        isUser ? Icons.person : Icons.smart_toy,
        color: Colors.white,
        size: 18,
      ),
    );
  }

  /// 构建消息头部
  Widget _buildMessageHeader(BuildContext context) {
    final theme = Theme.of(context);
    final isUser = message.role == 'user';
    
    return Container(
      padding: const EdgeInsets.fromLTRB(12, 8, 12, 4),
      child: Row(
        children: [
          Text(
            isUser ? '用户' : '岱宗AI',
            style: theme.textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: isUser ? Colors.white : theme.textTheme.bodyMedium?.color,
            ),
          ),
          if (message.isCreativeEdit) ...[
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withOpacity(0.2),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                '创作模式',
                style: theme.textTheme.bodySmall?.copyWith(
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                  color: theme.colorScheme.primary,
                ),
              ),
            ),
          ],
          const Spacer(),
          Text(
            _formatTime(message.timestamp),
            style: theme.textTheme.bodySmall?.copyWith(
              color: isUser 
                  ? Colors.white.withOpacity(0.7)
                  : theme.textTheme.bodySmall?.color?.withOpacity(0.6),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建消息内容
  Widget _buildMessageContent(BuildContext context) {
    final theme = Theme.of(context);
    final isUser = message.role == 'user';
    
    return Container(
      padding: const EdgeInsets.fromLTRB(12, 0, 12, 8),
      child: Text(
        message.content,
        style: theme.textTheme.bodyMedium?.copyWith(
          color: isUser ? Colors.white : theme.textTheme.bodyMedium?.color,
        ),
      ),
    );
  }

  /// 构建Diff视图
  Widget _buildDiffView(BuildContext context) {
    if (message.editResponse == null) return const SizedBox.shrink();
    
    return Container(
      margin: const EdgeInsets.fromLTRB(12, 0, 12, 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Divider(),
          Text(
            '修改建议详情',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          if (message.editResponse!.overallExplanation.isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                message.editResponse!.overallExplanation,
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ),
            const SizedBox(height: 8),
          ],
          DiffViewWidget(
            originalText: originalContent,
            modifications: message.editResponse!.modifications,
            onModificationStatusChanged: onModificationStatusChanged != null
                ? (modId, status) => onModificationStatusChanged!(message.id, modId, status)
                : null,
            onBatchStatusChanged: onBatchStatusChanged != null
                ? (status) => onBatchStatusChanged!(message.id, status)
                : null,
            compactMode: true,
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.fromLTRB(12, 0, 12, 12),
      child: Row(
        children: [
          const Spacer(),
          TextButton.icon(
            onPressed: () {
              if (onApplyModifications != null) {
                _showApplyConfirmDialog(context);
              }
            },
            icon: const Icon(Icons.check_circle, size: 16),
            label: const Text('应用修改'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  /// 显示应用修改确认对话框
  void _showApplyConfirmDialog(BuildContext context) {
    final acceptedCount = message.editResponse?.acceptedModifications.length ?? 0;
    
    if (acceptedCount == 0) {
      Get.snackbar('提示', '没有已接受的修改建议');
      return;
    }
    
    Get.dialog(
      AlertDialog(
        title: const Text('确认应用修改'),
        content: Text('确定要应用 $acceptedCount 项已接受的修改建议吗？此操作将直接修改章节内容。'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              if (onApplyModifications != null) {
                onApplyModifications!(message.id);
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('确认应用'),
          ),
        ],
      ),
    );
  }

  /// 格式化时间
  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final diff = now.difference(time);
    
    if (diff.inMinutes < 1) {
      return '刚刚';
    } else if (diff.inHours < 1) {
      return '${diff.inMinutes}分钟前';
    } else if (diff.inDays < 1) {
      return '${diff.inHours}小时前';
    } else {
      return '${time.month}/${time.day} ${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    }
  }
}
