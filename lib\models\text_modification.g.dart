// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'text_modification.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TextModification _$TextModificationFromJson(Map<String, dynamic> json) =>
    TextModification(
      id: json['id'] as String,
      type: $enumDecode(_$ModificationTypeEnumMap, json['type']),
      startLine: (json['startLine'] as int?) ?? (json['start_line'] as int? ?? 1),
      endLine: (json['endLine'] as int?) ?? (json['end_line'] as int? ?? 1),
      originalText: (json['originalText'] as String?) ?? (json['original_text'] as String? ?? ''),
      newText: (json['newText'] as String?) ?? (json['new_text'] as String? ?? ''),
      reason: (json['reason'] as String?) ?? '',
      status: $enumDecodeNullable(_$ModificationStatusEnumMap, json['status']) ??
          ModificationStatus.pending,
    );

Map<String, dynamic> _$TextModificationToJson(TextModification instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': _$ModificationTypeEnumMap[instance.type]!,
      'startLine': instance.startLine,
      'endLine': instance.endLine,
      'originalText': instance.originalText,
      'newText': instance.newText,
      'reason': instance.reason,
      'status': _$ModificationStatusEnumMap[instance.status]!,
    };

const _$ModificationTypeEnumMap = {
  ModificationType.replace: 'replace',
  ModificationType.insert: 'insert',
  ModificationType.delete: 'delete',
};

const _$ModificationStatusEnumMap = {
  ModificationStatus.pending: 'pending',
  ModificationStatus.accepted: 'accepted',
  ModificationStatus.rejected: 'rejected',
  ModificationStatus.applied: 'applied',
};

CreativeEditResponse _$CreativeEditResponseFromJson(
        Map<String, dynamic> json) =>
    CreativeEditResponse(
      responseType: (json['responseType'] as String?) ?? (json['response_type'] as String? ?? ''),
      summary: (json['summary'] as String?) ?? '',
      modifications: (json['modifications'] as List<dynamic>?)
          ?.map((e) => TextModification.fromJson(e as Map<String, dynamic>))
          .toList() ?? [],
      overallExplanation: (json['overallExplanation'] as String?) ?? (json['overall_explanation'] as String? ?? ''),
    );

Map<String, dynamic> _$CreativeEditResponseToJson(
        CreativeEditResponse instance) =>
    <String, dynamic>{
      'responseType': instance.responseType,
      'summary': instance.summary,
      'modifications': instance.modifications,
      'overallExplanation': instance.overallExplanation,
    };

K $enumDecode<K, V>(
  Map<K, V> enumValues,
  Object? source, {
  K? unknownValue,
}) {
  if (source == null) {
    throw ArgumentError(
      'A value must be provided. Supported values: '
      '${enumValues.values.join(', ')}',
    );
  }

  return enumValues.entries.singleWhere(
    (e) => e.value == source,
    orElse: () {
      if (unknownValue == null) {
        throw ArgumentError(
          '`$source` is not one of the supported values: '
          '${enumValues.values.join(', ')}',
        );
      }
      return MapEntry(unknownValue, enumValues.values.first);
    },
  ).key;
}

K? $enumDecodeNullable<K, V>(
  Map<K, V> enumValues,
  dynamic source, {
  K? unknownValue,
}) {
  if (source == null) {
    return null;
  }
  return $enumDecode<K, V>(enumValues, source, unknownValue: unknownValue);
}
